{"go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "go.gopath": "", "go.goroot": "", "go.lintOnSave": "package", "go.vetOnSave": "package", "go.buildOnSave": "off", "go.testOnSave": false, "go.coverOnSave": false, "go.formatTool": "goimports", "go.lintTool": "golint", "go.vetFlags": [], "go.testFlags": ["-v"], "go.buildFlags": [], "go.installDependenciesWhenBuilding": true, "go.autocompleteUnimportedPackages": true, "go.gocodePackageLookupMode": "go", "go.gotoSymbol.includeImports": true, "go.gotoSymbol.includeGoroot": true, "go.useCodeSnippetsOnFunctionSuggest": true, "go.inferGopath": true, "go.delveConfig": {"dlvLoadConfig": {"followPointers": true, "maxVariableRecurse": 1, "maxStringLen": 64, "maxArrayValues": 64, "maxStructFields": -1}, "apiVersion": 2, "showGlobalVariables": true}, "files.associations": {"*.go": "go", "wails.json": "json"}, "files.exclude": {"**/node_modules": true, "**/build/bin": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/build/bin": true, "**/frontend/dist": true, "**/frontend/wailsjs": true}, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "emmet.includeLanguages": {"vue": "html", "javascript": "javascriptreact"}, "terminal.integrated.env.windows": {"CGO_ENABLED": "1"}, "terminal.integrated.defaultProfile.windows": "Command Prompt"}