{"version": "0.2.0", "configurations": [{"name": "Debug Wails App (Development)", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/main.go", "args": [], "env": {"WAILS_DEBUG": "true", "CGO_ENABLED": "1"}, "cwd": "${workspaceFolder}", "showLog": true, "console": "integratedTerminal", "preLaunchTask": "Build Frontend for Debug"}, {"name": "Debug Wails Backend Only", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/main.go", "args": [], "env": {"WAILS_DEBUG": "true", "CGO_ENABLED": "1"}, "cwd": "${workspaceFolder}", "showLog": true, "console": "integratedTerminal", "stopOnEntry": false}, {"name": "Debug with Uninstall Flag", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/main.go", "args": ["--uninstall"], "env": {"WAILS_DEBUG": "true", "CGO_ENABLED": "1"}, "cwd": "${workspaceFolder}", "showLog": true, "console": "integratedTerminal"}, {"name": "Attach to Running Process", "type": "go", "request": "attach", "mode": "local", "processId": 0}]}