{"version": "2.0.0", "tasks": [{"label": "Run Wails Dev", "type": "shell", "command": "wails", "args": ["dev"], "group": "build", "isBackground": true, "problemMatcher": {"pattern": [], "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": ".*"}}, "presentation": {"reveal": "always", "panel": "shared", "focus": false}, "options": {"shell": {"executable": "cmd.exe", "args": ["/c"]}}}, {"label": "Build Frontend for Debug", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"reveal": "silent", "panel": "shared"}, "problemMatcher": []}, {"label": "Install Frontend Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Build Wails App", "type": "shell", "command": "wails", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": [], "options": {"shell": {"executable": "cmd.exe", "args": ["/c"]}}}, {"label": "Clean Build", "type": "shell", "command": "wails", "args": ["build", "-clean"], "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": [], "options": {"shell": {"executable": "cmd.exe", "args": ["/c"]}}}, {"label": "Run Frontend Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "isBackground": true, "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Go: Test All", "type": "shell", "command": "go", "args": ["test", "./..."], "group": "test", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": "$go"}]}