[{"key": "ctrl+shift+r", "command": "workbench.action.tasks.runTask", "args": "Run Wails Dev"}, {"key": "ctrl+shift+b", "command": "workbench.action.tasks.runTask", "args": "Build Wails App"}, {"key": "ctrl+shift+t", "command": "workbench.action.tasks.runTask", "args": "Go: Test All"}, {"key": "ctrl+shift+f", "command": "workbench.action.tasks.runTask", "args": "Run Frontend Dev Server"}, {"key": "f5", "command": "workbench.action.debug.start", "when": "!inDebugMode"}, {"key": "shift+f5", "command": "workbench.action.debug.stop", "when": "inDebugMode"}]